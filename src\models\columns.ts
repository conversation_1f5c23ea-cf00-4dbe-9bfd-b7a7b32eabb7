
import { DateTime } from 'luxon';

// Interface pour la structure des colonnes
type Column = {
  name: string;
  required?: boolean;
  label: string;
  align?: "left" | "right" | "center";
  field: string | ((row: any) => any);
  sortable?: boolean;
  format?: (value: any) => string;
  sort?: (a: any, b: any, rowA: any, rowB: any) => number;
  rawSort?: (a: any, b: any) => number;
  style?: string;
  classes?: string;
  headerClasses?: string;
  headerStyles?: string;
};

// Colonnes pour la table `users`
export const usersTableColumns: Column[] = [
  { name: 'public_id', label: 'ID Public', field: 'public_id', required: true, sortable: true, align: "left" },
  { name: 'username', label: 'Nom d\'utilisateur', field: 'username', sortable: true, align: "left" },
  { name: 'email', label: 'Email', field: 'email', sortable: true, align: "left" },
  { name: 'phone', label: 'Téléphone', field: 'phone', sortable: true, align: "left" },
  {
    name: 'role',
    label: 'Rôle',
    field: (row: any) => row.role?.name || '',
    sortable: true,
    align: "left",
    sort: (a: string, b: string) => a.localeCompare(b)
  },
  {
    name: 'country',
    label: 'Pays',
    field: (row: any) => row.country?.name || '',
    sortable: true,
    align: "left",
    sort: (a: string, b: string) => a.localeCompare(b)
  },
  {
    name: 'email_verified_at',
    label: 'Email vérifié le',
    field: 'email_verified_at',
    sortable: true,
    align: "left",
    format: (val: DateTime) => val ? val.toLocaleString(DateTime.DATETIME_SHORT) : ''
  },
  {
    name: 'phone_verified_at',
    label: 'Téléphone vérifié le',
    field: 'phone_verified_at',
    sortable: true,
    align: "left",
    format: (val: DateTime) => val ? val.toLocaleString(DateTime.DATETIME_SHORT) : ''
  },
  {
    name: 'is_online',
    label: 'En ligne',
    field: 'is_online',
    required: true,
    sortable: true,
    align: "left",
    format: (val: boolean) => val ? 'Oui' : 'Non'
  },
  { name: 'status', label: 'Statut', field: 'status', required: true, sortable: true, align: "left" },
  {
    name: 'created_at',
    label: 'Créé le',
    field: 'created_at',
    required: true,
    sortable: true,
    align: "left",
    format: (val: DateTime) => val ? val.toLocaleString(DateTime.DATETIME_SHORT) : ''
  },
  { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: "left" }
];

// Colonnes pour la table `admins`
export const adminsTableColumns: Column[] = [
  { name: 'public_id', label: 'ID Public', field: 'public_id', required: true, sortable: true, align: "left" },
  {
    name: 'user',
    label: 'Utilisateur',
    field: (row: any) => row.user?.username || row.user?.email || '',
    required: true,
    sortable: true,
    align: "left",
    sort: (a: string, b: string) => a.localeCompare(b)
  },
  { name: 'access_type', label: 'Type d\'accès', field: 'access_type', sortable: true, align: "left" },
  { name: 'last_name', label: 'Nom', field: 'last_name', required: true, sortable: true, align: "left" },
  { name: 'first_name', label: 'Prénom', field: 'first_name', required: true, sortable: true, align: "left" },
  { name: 'phone', label: 'Téléphone', field: 'phone', sortable: true, align: "left" },
  { name: 'email', label: 'Email', field: 'email', required: true, sortable: true, align: "left" },
  {
    name: 'created_at',
    label: 'Créé le',
    field: 'created_at',
    sortable: true,
    align: "left",
    format: (val: DateTime) => val ? val.toLocaleString(DateTime.DATETIME_SHORT) : ''
  },
  { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: "left" }
];

// Colonnes pour la table `sellers`
export const sellersTableColumns: Column[] = [
  { name: 'public_id', label: 'ID Public', field: 'public_id', required: true, sortable: true, align: "left" },
  {
    name: 'user',
    label: 'Utilisateur',
    field: (row: any) => row.user?.username || row.user?.email || '',
    required: true,
    sortable: true,
    align: "left",
    sort: (a: string, b: string) => a.localeCompare(b)
  },
  {
    name: 'country',
    label: 'Pays',
    field: (row: any) => row.country?.name || '',
    required: true,
    sortable: true,
    align: "left",
    sort: (a: string, b: string) => a.localeCompare(b)
  },
  { name: 'first_name', label: 'Prénom', field: 'first_name', required: true, sortable: true, align: "left" },
  { name: 'last_name', label: 'Nom', field: 'last_name', required: true, sortable: true, align: "left" },
  { name: 'phone', label: 'Téléphone', field: 'phone', sortable: true, align: "left" },
  { name: 'email', label: 'Email', field: 'email', required: true, sortable: true, align: "left" },
  { name: 'birthdate', label: 'Date de naissance', field: 'birthdate', sortable: true, align: "left" },
  { name: 'gender', label: 'Genre', field: 'gender', sortable: true, align: "left" },
  { name: 'address', label: 'Adresse', field: 'address', sortable: true, align: "left" },
  { name: 'city', label: 'Ville', field: 'city', sortable: true, align: "left" },
  { name: 'state', label: 'État', field: 'state', sortable: true, align: "left" },
  {
    name: 'is_verified',
    label: 'Vérifié',
    field: 'is_verified',
    required: true,
    sortable: true,
    align: "left",
    format: (val: boolean) => val ? 'Oui' : 'Non'
  },
  { name: 'status', label: 'Statut', field: 'status', required: true, sortable: true, align: "left" },
  {
    name: 'created_at',
    label: 'Créé le',
    field: 'created_at',
    sortable: true,
    align: "left",
    format: (val: DateTime) => val ? val.toLocaleString(DateTime.DATETIME_SHORT) : ''
  },
  { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: "left" }
];

// Colonnes pour la table `customers`
export const customersTableColumns: Column[] = [
  { name: 'public_id', label: 'ID Public', field: 'public_id', required: true, sortable: true, align: "left" },
  {
    name: 'user',
    label: 'Utilisateur',
    field: (row: any) => row.user?.username || row.user?.email || '',
    required: true,
    sortable: true,
    align: "left",
    sort: (a: string, b: string) => a.localeCompare(b)
  },
  {
    name: 'country',
    label: 'Pays',
    field: (row: any) => row.country?.name || '',
    required: true,
    sortable: true,
    align: "left",
    sort: (a: string, b: string) => a.localeCompare(b)
  },
  { name: 'first_name', label: 'Prénom', field: 'first_name', required: true, sortable: true, align: "left" },
  { name: 'last_name', label: 'Nom', field: 'last_name', required: true, sortable: true, align: "left" },
  { name: 'phone', label: 'Téléphone', field: 'phone', sortable: true, align: "left" },
  { name: 'email', label: 'Email', field: 'email', sortable: true, align: "left" },
  { name: 'birthdate', label: 'Date de naissance', field: 'birthdate', sortable: true, align: "left" },
  { name: 'gender', label: 'Genre', field: 'gender', sortable: true, align: "left" },
  { name: 'address', label: 'Adresse', field: 'address', sortable: true, align: "left" },
  { name: 'city', label: 'Ville', field: 'city', sortable: true, align: "left" },
  { name: 'state', label: 'État', field: 'state', sortable: true, align: "left" },
  { name: 'status', label: 'Statut', field: 'status', required: true, sortable: true, align: "left" },
  {
    name: 'created_at',
    label: 'Créé le',
    field: 'created_at',
    sortable: true,
    align: "left",
    format: (val: DateTime) => val ? val.toLocaleString(DateTime.DATETIME_SHORT) : ''
  },
  { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: "left" }
];

// Colonnes pour la table `categories`
export const categoriesTableColumns: Column[] = [
  // { name: 'public_id', label: 'ID Public', field: 'public_id', required: true, sortable: true, align: "left" },
  { name: 'name', label: 'Nom', field: 'name', required: true, sortable: true, align: "left" },
  {
    name: 'domain',
    label: 'Domaine',
    field: (row: any) => row.domain?.name || '',
    required: true,
    sortable: true,
    align: "left",
    sort: (a: string, b: string) => a.localeCompare(b)
  },
  {
    name: 'parent',
    label: 'Parent',
    field: (row: any) => row.parent?.name || '',
    sortable: true,
    align: "left",
    sort: (a: string, b: string) => a.localeCompare(b)
  },
  { name: 'description', label: 'Description', field: 'description', sortable: true, align: "left" },
  // { name: 'slug', label: 'Slug', field: 'slug', sortable: true, align: "left" },
  { name: 'image', label: 'Image', field: 'image', sortable: true, align: "center" },
  {
    name: 'created_at',
    label: 'Créé le',
    field: 'created_at',
    sortable: true,
    align: "left",
    format: (val: DateTime) => val ? val.toLocaleString(DateTime.DATETIME_SHORT) : ''
  },
  { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: "left" }
];

// Colonnes pour la table `productTypes`
export const productTypesTableColumns: Column[] = [
  { name: 'public_id', label: 'ID Public', field: 'public_id', required: true, sortable: true, align: "left" },
  { name: 'name', label: 'Nom', field: 'name', required: true, sortable: true, align: "left" },
  {
    name: 'category',
    label: 'Catégorie',
    field: (row: any) => row.category?.name || '',
    required: true,
    sortable: true,
    align: "left",
    sort: (a: string, b: string) => a.localeCompare(b)
  },
  { name: 'description', label: 'Description', field: 'description', sortable: true, align: "left" },
  { name: 'slug', label: 'Slug', field: 'slug', sortable: true, align: "left" },
  {
    name: 'created_at',
    label: 'Créé le',
    field: 'created_at',
    sortable: true,
    align: "left",
    format: (val: DateTime) => val ? val.toLocaleString(DateTime.DATETIME_SHORT) : ''
  },
  { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: "left" }
];

// Colonnes pour la table `tags`
export const tagsTableColumns: Column[] = [
  { name: 'public_id', label: 'ID Public', field: 'public_id', required: true, sortable: true, align: "left" },
  { name: 'name', label: 'Nom', field: 'name', required: true, sortable: true, align: "left" },
  {
    name: 'created_at',
    label: 'Créé le',
    field: 'created_at',
    sortable: true,
    align: "left",
    format: (val: DateTime) => val ? val.toLocaleString(DateTime.DATETIME_SHORT) : ''
  },
  { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: "left" }
];

// Colonnes pour la table `shops`
export const shopsTableColumns: Column[] = [
  { name: 'public_id', label: 'ID Public', field: 'public_id', required: true, sortable: true, align: "left" },
  { name: 'name', label: 'Nom', field: 'name', required: true, sortable: true, align: "left" },
  {
    name: 'domain',
    label: 'Domaine',
    field: (row: any) => row.domain?.name || '',
    sortable: true,
    align: "left",
    sort: (a: string, b: string) => a.localeCompare(b)
  },
  {
    name: 'country',
    label: 'Pays',
    field: (row: any) => row.country?.name || '',
    required: true,
    sortable: true,
    align: "left",
    sort: (a: string, b: string) => a.localeCompare(b)
  },
  { name: 'description', label: 'Description', field: 'description', sortable: true, align: "left" },
  { name: 'slug', label: 'Slug', field: 'slug', sortable: true, align: "left" },
  { name: 'email', label: 'Email', field: 'email', sortable: true, align: "left" },
  { name: 'phone', label: 'Téléphone', field: 'phone', sortable: true, align: "left" },
  { name: 'city', label: 'Ville', field: 'city', sortable: true, align: "left" },
  { name: 'state', label: 'État', field: 'state', sortable: true, align: "left" },
  { name: 'address', label: 'Adresse', field: 'address', required: true, sortable: true, align: "left" },
  {
    name: 'created_at',
    label: 'Créé le',
    field: 'created_at',
    sortable: true,
    align: "left",
    format: (val: DateTime) => val ? val.toLocaleString(DateTime.DATETIME_SHORT) : ''
  },
  { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: "left" }
];

// Colonnes pour la table `products`
export const productsTableColumns: Column[] = [
  { name: 'public_id', label: 'ID Public', field: 'public_id', required: true, sortable: true, align: "left" },
  { name: 'name', label: 'Nom', field: 'name', required: true, sortable: true, align: "left" },
  {
    name: 'shop',
    label: 'Boutique',
    field: (row: any) => row.shop?.name || '',
    required: true,
    sortable: true,
    align: "left",
    sort: (a: string, b: string) => a.localeCompare(b)
  },
  {
    name: 'product_type',
    label: 'Type de produit',
    field: (row: any) => row.product_type?.name || '',
    required: true,
    sortable: true,
    align: "left",
    sort: (a: string, b: string) => a.localeCompare(b)
  },
  { name: 'description', label: 'Description', field: 'description', sortable: true, align: "left" },
  { name: 'sku', label: 'SKU', field: 'sku', sortable: true, align: "left" },
  { name: 'price', label: 'Prix', field: 'price', required: true, sortable: true, align: "left" },
  { name: 'sale_price', label: 'Prix promo', field: 'sale_price', sortable: true, align: "left" },
  { name: 'quantity', label: 'Quantité', field: 'quantity', sortable: true, align: "left" },
  { name: 'condition', label: 'Condition', field: 'condition', required: true, sortable: true, align: "left" },
  { name: 'status', label: 'Statut', field: 'status', required: true, sortable: true, align: "left" },
  {
    name: 'is_digital',
    label: 'Numérique',
    field: 'is_digital',
    required: true,
    sortable: true,
    align: "left",
    format: (val: boolean) => val ? 'Oui' : 'Non'
  },
  {
    name: 'is_featured',
    label: 'Mis en avant',
    field: 'is_featured',
    required: true,
    sortable: true,
    align: "left",
    format: (val: boolean) => val ? 'Oui' : 'Non'
  },
  {
    name: 'created_at',
    label: 'Créé le',
    field: 'created_at',
    sortable: true,
    align: "left",
    format: (val: DateTime) => val ? val.toLocaleString(DateTime.DATETIME_SHORT) : ''
  },
  { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: "left" }
];

// Colonnes pour la table `orders`
export const ordersTableColumns: Column[] = [
  { name: 'public_id', label: 'ID Public', field: 'public_id', required: true, sortable: true, align: "left" },
  {
    name: 'customer',
    label: 'Client',
    field: (row: any) => row.customer?.first_name + ' ' + row.customer?.last_name || '',
    required: true,
    sortable: true,
    align: "left",
    sort: (a: string, b: string) => a.localeCompare(b)
  },
  { name: 'subtotal_amount', label: 'Montant hors taxes', field: 'subtotal_amount', required: true, sortable: true, align: "left" },
  { name: 'tax', label: 'Taxes', field: 'tax', required: true, sortable: true, align: "left" },
  { name: 'shipping_cost', label: 'Frais de livraison', field: 'shipping_cost', required: true, sortable: true, align: "left" },
  { name: 'total_amount', label: 'Montant total', field: 'total_amount', required: true, sortable: true, align: "left" },
  { name: 'currency', label: 'Devise', field: 'currency', required: true, sortable: true, align: "left" },
  { name: 'status', label: 'Statut', field: 'status', required: true, sortable: true, align: "left" },
  { name: 'payment_status', label: 'Statut paiement', field: 'payment_status', required: true, sortable: true, align: "left" },
  { name: 'delivery_status', label: 'Statut livraison', field: 'delivery_status', required: true, sortable: true, align: "left" },
  {
    name: 'is_paid',
    label: 'Payé',
    field: 'is_paid',
    required: true,
    sortable: true,
    align: "left",
    format: (val: boolean) => val ? 'Oui' : 'Non'
  },
  {
    name: 'requires_shipping',
    label: 'Livraison requise',
    field: 'requires_shipping',
    required: true,
    sortable: true,
    align: "left",
    format: (val: boolean) => val ? 'Oui' : 'Non'
  },
  {
    name: 'created_at',
    label: 'Créé le',
    field: 'created_at',
    sortable: true,
    align: "left",
    format: (val: DateTime) => val ? val.toLocaleString(DateTime.DATETIME_SHORT) : ''
  },
  { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: "left" }
];

// Colonnes pour la table `transactions`
export const transactionsTableColumns: Column[] = [
  { name: 'public_id', label: 'ID Public', field: 'public_id', required: true, sortable: true, align: "left" },
  {
    name: 'order',
    label: 'Commande',
    field: (row: any) => row.order?.public_id || '',
    required: true,
    sortable: true,
    align: "left",
    sort: (a: string, b: string) => a.localeCompare(b)
  },
  {
    name: 'customer',
    label: 'Client',
    field: (row: any) => row.customer?.first_name + ' ' + row.customer?.last_name || '',
    sortable: true,
    align: "left",
    sort: (a: string, b: string) => a.localeCompare(b)
  },
  { name: 'amount', label: 'Montant', field: 'amount', required: true, sortable: true, align: "left" },
  { name: 'fees', label: 'Frais', field: 'fees', sortable: true, align: "left" },
  { name: 'currency', label: 'Devise', field: 'currency', required: true, sortable: true, align: "left" },
  { name: 'payment_method', label: 'Méthode de paiement', field: 'payment_method', required: true, sortable: true, align: "left" },
  { name: 'status', label: 'Statut', field: 'status', required: true, sortable: true, align: "left" },
  {
    name: 'paid_at',
    label: 'Payé le',
    field: 'paid_at',
    sortable: true,
    align: "left",
    format: (val: DateTime) => val ? val.toLocaleString(DateTime.DATETIME_SHORT) : ''
  },
  {
    name: 'created_at',
    label: 'Créé le',
    field: 'created_at',
    sortable: true,
    align: "left",
    format: (val: DateTime) => val ? val.toLocaleString(DateTime.DATETIME_SHORT) : ''
  },
  { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: "left" }
];

// Colonnes pour la table `wallets`
export const walletsTableColumns: Column[] = [
  { name: 'public_id', label: 'ID Public', field: 'public_id', required: true, sortable: true, align: "left" },
  { name: 'name', label: 'Nom', field: 'name', sortable: true, align: "left" },
  { name: 'owner_type', label: 'Type de propriétaire', field: 'owner_type', required: true, sortable: true, align: "left" },
  { name: 'currency', label: 'Devise', field: 'currency', required: true, sortable: true, align: "left" },
  { name: 'balance', label: 'Solde', field: 'balance', required: true, sortable: true, align: "left" },
  { name: 'status', label: 'Statut', field: 'status', required: true, sortable: true, align: "left" },
  {
    name: 'has_secret_code',
    label: 'Code secret',
    field: 'has_secret_code',
    required: true,
    sortable: true,
    align: "left",
    format: (val: boolean) => val ? 'Oui' : 'Non'
  },
  {
    name: 'created_at',
    label: 'Créé le',
    field: 'created_at',
    sortable: true,
    align: "left",
    format: (val: DateTime) => val ? val.toLocaleString(DateTime.DATETIME_SHORT) : ''
  },
  { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: "left" }
];

// Colonnes pour la table `withdrawals`
export const withdrawalsTableColumns: Column[] = [
  { name: 'id', label: 'ID', field: 'id', required: true, sortable: true, align: "left" },
  {
    name: 'wallet',
    label: 'Portefeuille',
    field: (row: any) => row.wallet?.name || '',
    required: true,
    sortable: true,
    align: "left",
    sort: (a: string, b: string) => a.localeCompare(b)
  },
  { name: 'amount', label: 'Montant', field: 'amount', required: true, sortable: true, align: "left" },
  { name: 'fees', label: 'Frais', field: 'fees', sortable: true, align: "left" },
  { name: 'status', label: 'Statut', field: 'status', required: true, sortable: true, align: "left" },
  {
    name: 'created_at',
    label: 'Créé le',
    field: 'created_at',
    sortable: true,
    align: "left",
    format: (val: DateTime) => val ? val.toLocaleString(DateTime.DATETIME_SHORT) : ''
  },
  { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: "left" }
];

// Colonnes pour la table `reviews`
export const reviewsTableColumns: Column[] = [
  { name: 'id', label: 'ID', field: 'id', required: true, sortable: true, align: "left" },
  {
    name: 'customer',
    label: 'Client',
    field: (row: any) => row.customer?.first_name + ' ' + row.customer?.last_name || '',
    sortable: true,
    align: "left",
    sort: (a: string, b: string) => a.localeCompare(b)
  },
  {
    name: 'product',
    label: 'Produit',
    field: (row: any) => row.product?.name || '',
    sortable: true,
    align: "left",
    sort: (a: string, b: string) => a.localeCompare(b)
  },
  {
    name: 'shop',
    label: 'Boutique',
    field: (row: any) => row.shop?.name || '',
    sortable: true,
    align: "left",
    sort: (a: string, b: string) => a.localeCompare(b)
  },
  { name: 'rating', label: 'Note', field: 'rating', required: true, sortable: true, align: "left" },
  { name: 'comment', label: 'Commentaire', field: 'comment', sortable: true, align: "left" },
  { name: 'likes', label: 'Likes', field: 'likes', required: true, sortable: true, align: "left" },
  { name: 'dislikes', label: 'Dislikes', field: 'dislikes', required: true, sortable: true, align: "left" },
  {
    name: 'created_at',
    label: 'Créé le',
    field: 'created_at',
    sortable: true,
    align: "left",
    format: (val: DateTime) => val ? val.toLocaleString(DateTime.DATETIME_SHORT) : ''
  },
  // { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: "left" }
];

// Colonnes pour la table `questions`
export const questionsTableColumns: Column[] = [
  // { name: 'id', label: 'ID', field: 'id', required: true, sortable: true, align: "left" },
  {
    name: 'customer',
    label: 'Client',
    field: (row: any) => row.customer?.first_name + ' ' + row.customer?.last_name || '',
    sortable: true,
    align: "left",
    sort: (a: string, b: string) => a.localeCompare(b)
  },
  {
    name: 'product',
    label: 'Produit',
    field: (row: any) => row.product?.name || '',
    sortable: true,
    align: "left",
    sort: (a: string, b: string) => a.localeCompare(b)
  },
  {
    name: 'shop',
    label: 'Boutique',
    field: (row: any) => row.shop?.name || '',
    sortable: true,
    align: "left",
    sort: (a: string, b: string) => a.localeCompare(b)
  },
  { name: 'question', label: 'Question', field: 'question', required: true, sortable: true, align: "left" },
  // { name: 'answer', label: 'Réponse', field: 'answer', required: true, sortable: true, align: "left" },
  {
    name: 'is_published',
    label: 'Publié',
    field: 'is_published',
    required: true,
    sortable: true,
    align: "left",
    format: (val: boolean) => val ? 'Oui' : 'Non'
  },
  { name: 'views_count', label: 'Vues', field: 'views_count', required: true, sortable: true, align: "left" },
  { name: 'helpful_count', label: 'Utile', field: 'helpful_count', required: true, sortable: true, align: "left" },
  { name: 'not_helpful_count', label: 'Non utile', field: 'not_helpful_count', required: true, sortable: true, align: "left" },
  {
    name: 'created_at',
    label: 'Créé le',
    field: 'created_at',
    sortable: true,
    align: "left",
    format: (val: DateTime) => val ? val.toLocaleString(DateTime.DATETIME_SHORT) : ''
  },
  // { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: "left" }
];

// Colonnes pour la table `roles`
export const rolesTableColumns: Column[] = [
  { name: 'public_id', label: 'ID Public', field: 'public_id', required: true, sortable: true, align: "left" },
  { name: 'name', label: 'Nom', field: 'name', required: true, sortable: true, align: "left" },
  { name: 'slug', label: 'Slug', field: 'slug', sortable: true, align: "left" },
  { name: 'description', label: 'Description', field: 'description', sortable: true, align: "left" },
  {
    name: 'created_at',
    label: 'Créé le',
    field: 'created_at',
    sortable: true,
    align: "left",
    format: (val: DateTime) => val ? val.toLocaleString(DateTime.DATETIME_SHORT) : ''
  },
  { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: "left" }
];

// Colonnes pour la table `countries`
export const countriesTableColumns: Column[] = [
  { name: 'public_id', label: 'ID Public', field: 'public_id', required: true, sortable: true, align: "left" },
  { name: 'name', label: 'Nom', field: 'name', required: true, sortable: true, align: "left" },
  { name: 'code', label: 'Code', field: 'code', required: true, sortable: true, align: "left" },
  { name: 'phone_code', label: 'Code téléphone', field: 'phone_code', sortable: true, align: "left" },
  { name: 'currency', label: 'Devise', field: 'currency', required: true, sortable: true, align: "left" },
  {
    name: 'is_active',
    label: 'Actif',
    field: 'is_active',
    required: true,
    sortable: true,
    align: "left",
    format: (val: boolean) => val ? 'Oui' : 'Non'
  },
  {
    name: 'created_at',
    label: 'Créé le',
    field: 'created_at',
    sortable: true,
    align: "left",
    format: (val: DateTime) => val ? val.toLocaleString(DateTime.DATETIME_SHORT) : ''
  },
  { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: "left" }
];

// Colonnes pour la table `domains`
export const domainsTableColumns: Column[] = [
  { name: 'public_id', label: 'ID Public', field: 'public_id', required: true, sortable: true, align: "left" },
  { name: 'name', label: 'Nom', field: 'name', required: true, sortable: true, align: "left" },
  { name: 'description', label: 'Description', field: 'description', sortable: true, align: "left" },
  {
    name: 'is_active',
    label: 'Actif',
    field: 'is_active',
    required: true,
    sortable: true,
    align: "left",
    format: (val: boolean) => val ? 'Oui' : 'Non'
  },
  {
    name: 'created_at',
    label: 'Créé le',
    field: 'created_at',
    sortable: true,
    align: "left",
    format: (val: DateTime) => val ? val.toLocaleString(DateTime.DATETIME_SHORT) : ''
  },
  { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: "left" }
];

// Colonnes pour la table `payment_gateways`
export const paymentGatewaysTableColumns: Column[] = [
  { name: 'id', label: 'ID', field: 'id', required: true, sortable: true, align: "left" },
  { name: 'name', label: 'Nom', field: 'name', sortable: true, align: "left" },
  { name: 'type_api', label: 'Type API', field: 'type_api', required: true, sortable: true, align: "left" },
  { name: 'mode', label: 'Mode', field: 'mode', required: true, sortable: true, align: "left" },
  {
    name: 'status',
    label: 'Statut',
    field: 'status',
    required: true,
    sortable: true,
    align: "left",
    format: (val: boolean) => val ? 'Actif' : 'Inactif'
  },
  {
    name: 'created_at',
    label: 'Créé le',
    field: 'created_at',
    sortable: true,
    align: "left",
    format: (val: DateTime) => val ? val.toLocaleString(DateTime.DATETIME_SHORT) : ''
  },
  { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: "left" }
];

// Export de toutes les colonnes pour faciliter l'import
export const tableColumns = {
  users: usersTableColumns,
  admins: adminsTableColumns,
  sellers: sellersTableColumns,
  customers: customersTableColumns,
  categories: categoriesTableColumns,
  productTypes: productTypesTableColumns,
  tags: tagsTableColumns,
  shops: shopsTableColumns,
  products: productsTableColumns,
  orders: ordersTableColumns,
  transactions: transactionsTableColumns,
  wallets: walletsTableColumns,
  withdrawals: withdrawalsTableColumns,
  reviews: reviewsTableColumns,
  questions: questionsTableColumns,
  roles: rolesTableColumns,
  countries: countriesTableColumns,
  domains: domainsTableColumns,
  paymentGateways: paymentGatewaysTableColumns
};

// Export des types pour TypeScript
export type TableColumnKeys = keyof typeof tableColumns;
export type { Column };
