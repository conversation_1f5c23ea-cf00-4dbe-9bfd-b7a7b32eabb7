<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';

// Interface pour définir la structure d'une boutique
interface Shop {
  id: number;
  name: string;
  description: string;
  logo?: string;
  status: 'active' | 'inactive';
  address?: string;
}

// Props du composant
interface Props {
  modelValue?: Shop | null;
  disabled?: boolean;
  outlined?: boolean;
  dense?: boolean;
  label?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: null,
  disabled: false,
  outlined: true,
  dense: false,
  label: 'Sélectionner une boutique'
});

// Émissions d'événements
const emit = defineEmits<{
  'update:modelValue': [shop: Shop | null];
  'shop-changed': [shop: Shop];
}>();

// État local
const selectedShop = ref<Shop | null>(props.modelValue);
const isLoading = ref(false);

// Données de test pour les boutiques
const testShops = ref<Shop[]>([
  {
    id: 1,
    name: 'Boutique Mode & Style',
    description: 'Vêtements et accessoires de mode',
    status: 'active',
    address: 'Dakar, Sénégal',
    logo: 'https://via.placeholder.com/40x40/4CAF50/FFFFFF?text=MS'
  },
  {
    id: 2,
    name: 'Électronique Plus',
    description: 'Appareils électroniques et gadgets',
    status: 'active',
    address: 'Thiès, Sénégal',
    logo: 'https://via.placeholder.com/40x40/2196F3/FFFFFF?text=EP'
  },
  {
    id: 3,
    name: 'Alimentation Bio',
    description: 'Produits alimentaires biologiques',
    status: 'active',
    address: 'Saint-Louis, Sénégal',
    logo: 'https://via.placeholder.com/40x40/FF9800/FFFFFF?text=AB'
  },
  {
    id: 4,
    name: 'Artisanat Local',
    description: 'Produits artisanaux traditionnels',
    status: 'inactive',
    address: 'Kaolack, Sénégal',
    logo: 'https://via.placeholder.com/40x40/9C27B0/FFFFFF?text=AL'
  },
  {
    id: 5,
    name: 'Cosmétiques & Beauté',
    description: 'Produits de beauté et cosmétiques',
    status: 'active',
    address: 'Ziguinchor, Sénégal',
    logo: 'https://via.placeholder.com/40x40/E91E63/FFFFFF?text=CB'
  }
]);

// Boutiques actives uniquement
const activeShops = computed(() => 
  testShops.value.filter(shop => shop.status === 'active')
);

// Gestion de la sélection
const onShopSelect = (shop: Shop) => {
  selectedShop.value = shop;
  emit('update:modelValue', shop);
  emit('shop-changed', shop);
};

// Simulation du chargement des boutiques
const loadShops = async () => {
  isLoading.value = true;
  // Simulation d'un appel API
  await new Promise(resolve => setTimeout(resolve, 1000));
  isLoading.value = false;
};

// Initialisation
onMounted(() => {
  loadShops();
});

// Computed pour l'affichage de la boutique sélectionnée
const displayValue = computed(() => {
  return selectedShop.value?.name || '';
});
</script>

<template>
  <div class="shop-selector">
    <q-select
      v-model="selectedShop"
      :options="activeShops"
      :label="label"
      :outlined="outlined"
      :dense="dense"
      :disabled="disabled || isLoading"
      :loading="isLoading"
      option-label="name"
      option-value="id"
      emit-value
      map-options
      clearable
      @update:model-value="onShopSelect"
      class="shop-select"
    >
      <!-- Template pour l'option sélectionnée -->
      <template v-slot:selected>
        <div v-if="selectedShop" class="selected-shop">
          <q-avatar size="24px" class="q-mr-sm">
            <q-img 
              :src="selectedShop.logo" 
              :alt="selectedShop.name"
              spinner-color="primary"
            />
          </q-avatar>
          <div class="shop-info">
            <div class="shop-name">{{ selectedShop.name }}</div>
            <div class="shop-description text-caption text-grey-6">
              {{ selectedShop.description }}
            </div>
          </div>
        </div>
      </template>

      <!-- Template pour les options dans la liste -->
      <template v-slot:option="scope">
        <q-item
          v-bind="scope.itemProps"
          class="shop-option"
        >
          <q-item-section avatar>
            <q-avatar size="32px">
              <q-img 
                :src="scope.opt.logo" 
                :alt="scope.opt.name"
                spinner-color="primary"
              />
            </q-avatar>
          </q-item-section>
          
          <q-item-section>
            <q-item-label class="shop-name">
              {{ scope.opt.name }}
            </q-item-label>
            <q-item-label caption class="shop-description">
              {{ scope.opt.description }}
            </q-item-label>
            <q-item-label caption class="shop-address text-grey-5">
              <q-icon name="place" size="12px" class="q-mr-xs" />
              {{ scope.opt.address }}
            </q-item-label>
          </q-item-section>

          <q-item-section side>
            <q-badge 
              :color="scope.opt.status === 'active' ? 'positive' : 'grey'" 
              :label="scope.opt.status === 'active' ? 'Actif' : 'Inactif'"
              rounded
            />
          </q-item-section>
        </q-item>
      </template>

      <!-- Template pour l'état de chargement -->
      <template v-slot:loading>
        <q-item>
          <q-item-section avatar>
            <q-skeleton type="QAvatar" />
          </q-item-section>
          <q-item-section>
            <q-skeleton type="text" />
            <q-skeleton type="text" width="60%" />
          </q-item-section>
        </q-item>
      </template>

      <!-- Template pour l'état vide -->
      <template v-slot:no-option>
        <q-item>
          <q-item-section class="text-grey">
            <div class="text-center">
              <q-icon name="store" size="48px" class="q-mb-sm" />
              <div>Aucune boutique disponible</div>
            </div>
          </q-item-section>
        </q-item>
      </template>
    </q-select>

    <!-- Informations sur la boutique sélectionnée -->
    <div v-if="selectedShop" class="selected-shop-info q-mt-sm">
      <q-card flat bordered class="q-pa-sm">
        <div class="row items-center">
          <q-avatar size="40px" class="q-mr-sm">
            <q-img 
              :src="selectedShop.logo" 
              :alt="selectedShop.name"
              spinner-color="primary"
            />
          </q-avatar>
          <div class="col">
            <div class="text-subtitle2 text-primary">{{ selectedShop.name }}</div>
            <div class="text-caption text-grey-6">{{ selectedShop.description }}</div>
            <div class="text-caption text-grey-5">
              <q-icon name="place" size="12px" class="q-mr-xs" />
              {{ selectedShop.address }}
            </div>
          </div>
          <q-badge 
            :color="selectedShop.status === 'active' ? 'positive' : 'grey'" 
            :label="selectedShop.status === 'active' ? 'Actif' : 'Inactif'"
            rounded
          />
        </div>
      </q-card>
    </div>
  </div>
</template>

<style scoped>
.shop-selector {
  width: 100%;
}

.shop-select {
  min-width: 250px;
}

.selected-shop {
  display: flex;
  align-items: center;
  width: 100%;
}

.shop-info {
  flex: 1;
  min-width: 0;
}

.shop-name {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.shop-description {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.shop-option {
  padding: 8px 16px;
}

.shop-option:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.selected-shop-info {
  max-width: 100%;
}

.shop-address {
  display: flex;
  align-items: center;
}
</style>
