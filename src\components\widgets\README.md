# ShopSelector Component

Un composant Vue 3 + Quasar pour sélectionner une boutique dans l'application MASSANOU MARKET.

## Fonctionnalités

- ✅ Sélection d'une boutique parmi une liste
- ✅ Affichage des informations détaillées (nom, description, logo, adresse)
- ✅ Données de test intégrées
- ✅ Événements personnalisés lors du changement de boutique
- ✅ Interface responsive et accessible
- ✅ Support TypeScript complet
- ✅ Intégration avec Quasar UI
- ✅ État de chargement et gestion d'erreurs
- ✅ Filtrage des boutiques actives/inactives

## Installation et Utilisation

### Import du composant

```vue
<script setup lang="ts">
import ShopSelector from 'src/components/widgets/ShopSelector.vue';
import type { Shop } from 'src/components/widgets/types';

const selectedShop = ref<Shop | null>(null);

const onShopChanged = (shop: Shop) => {
  console.log('Nouvelle boutique sélectionnée:', shop);
  // Votre logique ici
};
</script>

<template>
  <ShopSelector
    v-model="selectedShop"
    label="Choisissez votre boutique"
    @shop-changed="onShopChanged"
    outlined
  />
</template>
```

### Props disponibles

| Prop | Type | Défaut | Description |
|------|------|--------|-------------|
| `modelValue` | `Shop \| null` | `null` | Boutique sélectionnée (v-model) |
| `disabled` | `boolean` | `false` | Désactive le composant |
| `outlined` | `boolean` | `true` | Style outlined pour le select |
| `dense` | `boolean` | `false` | Taille compacte |
| `label` | `string` | `'Sélectionner une boutique'` | Label du select |

### Événements

| Événement | Payload | Description |
|-----------|---------|-------------|
| `update:modelValue` | `Shop \| null` | Émis lors du changement de valeur (v-model) |
| `shop-changed` | `Shop` | Émis quand une boutique est sélectionnée |

## Structure des données

### Interface Shop

```typescript
interface Shop {
  id: number;
  name: string;
  description: string;
  logo?: string;
  status: 'active' | 'inactive';
  address?: string;
  contact?: {
    phone?: string;
    email?: string;
    website?: string;
  };
  categories?: string[];
  owner?: {
    name: string;
    email: string;
    phone?: string;
  };
  createdAt?: Date | string;
  updatedAt?: Date | string;
  metadata?: Record<string, any>;
}
```

## Données de test

Le composant inclut 5 boutiques de test :

1. **Boutique Mode & Style** - Vêtements et accessoires (Dakar)
2. **Électronique Plus** - Appareils électroniques (Thiès)
3. **Alimentation Bio** - Produits biologiques (Saint-Louis)
4. **Artisanat Local** - Produits artisanaux (Kaolack) - *Inactive*
5. **Cosmétiques & Beauté** - Produits de beauté (Ziguinchor)

## Exemples d'utilisation

### Utilisation basique

```vue
<template>
  <ShopSelector v-model="selectedShop" />
</template>
```

### Avec gestion d'événements

```vue
<script setup lang="ts">
const selectedShop = ref<Shop | null>(null);

const handleShopChange = (shop: Shop) => {
  // Sauvegarder dans localStorage
  localStorage.setItem('currentShop', JSON.stringify(shop));
  
  // Rediriger vers le dashboard de la boutique
  router.push(`/shop/${shop.id}/dashboard`);
  
  // Mettre à jour le store global
  shopStore.setCurrentShop(shop);
};
</script>

<template>
  <ShopSelector
    v-model="selectedShop"
    @shop-changed="handleShopChange"
    label="Ma boutique"
    outlined
    dense
  />
</template>
```

### Intégration dans MainLayout

Pour intégrer le composant dans la sidebar du MainLayout :

```vue
<!-- Dans MainLayout.vue -->
<script setup lang="ts">
import ShopSelector from 'src/components/widgets/ShopSelector.vue';

const currentShop = ref<Shop | null>(null);

const onShopChanged = (shop: Shop) => {
  // Logique de changement de boutique
  console.log('Boutique changée:', shop);
};
</script>

<template>
  <q-drawer>
    <!-- Header avec logo -->
    <div class="absolute-top img-header">
      <!-- Contenu existant -->
    </div>
    
    <!-- Sélecteur de boutique -->
    <div class="q-pa-md" style="margin-top: 80px;">
      <ShopSelector
        v-model="currentShop"
        @shop-changed="onShopChanged"
        label="Boutique active"
        dense
        outlined
      />
    </div>
    
    <!-- Menu existant -->
    <q-scroll-area class="fit scrollarea">
      <!-- Contenu existant -->
    </q-scroll-area>
  </q-drawer>
</template>
```

## Personnalisation

### Styles CSS

Le composant utilise des classes CSS personnalisables :

```css
.shop-selector {
  /* Conteneur principal */
}

.shop-select {
  /* Select Quasar */
  min-width: 250px;
}

.selected-shop {
  /* Boutique sélectionnée affichée */
}

.shop-option {
  /* Options dans la liste déroulante */
}
```

### Modification des données de test

Pour modifier les boutiques de test, éditez le tableau `testShops` dans le composant ou utilisez les données depuis `src/components/widgets/types.ts`.

## Intégration avec API

Pour connecter le composant à une API réelle :

```vue
<script setup lang="ts">
import { onMounted } from 'vue';

const shops = ref<Shop[]>([]);
const isLoading = ref(false);

const loadShops = async () => {
  isLoading.value = true;
  try {
    const response = await fetch('/api/shops');
    shops.value = await response.json();
  } catch (error) {
    console.error('Erreur lors du chargement des boutiques:', error);
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => {
  loadShops();
});
</script>
```

## Tests

Pour tester le composant, vous pouvez utiliser le composant d'exemple :

```bash
# Importer ShopSelectorExample dans une route de test
import ShopSelectorExample from 'src/components/widgets/ShopSelectorExample.vue';
```

## Support et Contribution

Ce composant fait partie de l'application MASSANOU MARKET. Pour toute question ou amélioration, veuillez consulter la documentation du projet principal.
