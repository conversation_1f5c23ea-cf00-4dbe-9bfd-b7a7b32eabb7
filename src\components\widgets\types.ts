// Types et interfaces pour les composants widgets

/**
 * Interface représentant une boutique
 */
export interface Shop {
  /** Identifiant unique de la boutique */
  id: number;
  
  /** Nom de la boutique */
  name: string;
  
  /** Description de la boutique */
  description: string;
  
  /** URL du logo de la boutique (optionnel) */
  logo?: string;
  
  /** Statut de la boutique */
  status: 'active' | 'inactive' | 'pending' | 'suspended';
  
  /** Adresse de la boutique (optionnel) */
  address?: string;
  
  /** Informations de contact (optionnel) */
  contact?: {
    phone?: string;
    email?: string;
    website?: string;
  };
  
  /** Catégories de produits vendus (optionnel) */
  categories?: string[];
  
  /** Informations sur le propriétaire (optionnel) */
  owner?: {
    name: string;
    email: string;
    phone?: string;
  };
  
  /** Date de création (optionnel) */
  createdAt?: Date | string;
  
  /** Date de dernière mise à jour (optionnel) */
  updatedAt?: Date | string;
  
  /** Métadonnées supplémentaires (optionnel) */
  metadata?: Record<string, any>;
}

/**
 * Props pour le composant ShopSelector
 */
export interface ShopSelectorProps {
  /** Boutique sélectionnée (v-model) */
  modelValue?: Shop | null;
  
  /** Désactiver le composant */
  disabled?: boolean;
  
  /** Style outlined pour le select */
  outlined?: boolean;
  
  /** Taille dense pour le select */
  dense?: boolean;
  
  /** Label du select */
  label?: string;
  
  /** Boutiques disponibles (si on veut passer une liste custom) */
  shops?: Shop[];
  
  /** Afficher seulement les boutiques actives */
  activeOnly?: boolean;
  
  /** Permettre la sélection multiple */
  multiple?: boolean;
  
  /** Placeholder quand aucune boutique n'est sélectionnée */
  placeholder?: string;
}

/**
 * Événements émis par le composant ShopSelector
 */
export interface ShopSelectorEmits {
  /** Émis quand la valeur change (v-model) */
  'update:modelValue': [shop: Shop | null];
  
  /** Émis quand une boutique est sélectionnée */
  'shop-changed': [shop: Shop];
  
  /** Émis quand une boutique est désélectionnée */
  'shop-cleared': [];
  
  /** Émis lors du chargement des boutiques */
  'loading': [isLoading: boolean];
  
  /** Émis en cas d'erreur */
  'error': [error: Error];
}

/**
 * Options de configuration pour le ShopSelector
 */
export interface ShopSelectorConfig {
  /** URL de l'API pour charger les boutiques */
  apiUrl?: string;
  
  /** Clé de stockage local pour la boutique sélectionnée */
  storageKey?: string;
  
  /** Activer la sauvegarde automatique dans le localStorage */
  autoSave?: boolean;
  
  /** Délai de debounce pour la recherche (en ms) */
  searchDebounce?: number;
  
  /** Nombre maximum de boutiques à afficher */
  maxItems?: number;
  
  /** Activer la recherche */
  searchable?: boolean;
  
  /** Activer le tri automatique */
  sortable?: boolean;
  
  /** Critère de tri par défaut */
  sortBy?: keyof Shop;
  
  /** Ordre de tri */
  sortOrder?: 'asc' | 'desc';
}

/**
 * Réponse de l'API pour la liste des boutiques
 */
export interface ShopsApiResponse {
  /** Liste des boutiques */
  shops: Shop[];
  
  /** Nombre total de boutiques */
  total: number;
  
  /** Page actuelle */
  page?: number;
  
  /** Nombre d'éléments par page */
  limit?: number;
  
  /** Indique s'il y a plus de pages */
  hasMore?: boolean;
}

/**
 * Filtre pour la recherche de boutiques
 */
export interface ShopFilter {
  /** Recherche par nom */
  name?: string;
  
  /** Filtre par statut */
  status?: Shop['status'][];
  
  /** Filtre par catégories */
  categories?: string[];
  
  /** Filtre par adresse/localisation */
  location?: string;
  
  /** Date de création (après) */
  createdAfter?: Date | string;
  
  /** Date de création (avant) */
  createdBefore?: Date | string;
}

/**
 * Utilitaires pour les boutiques
 */
export class ShopUtils {
  /**
   * Vérifie si une boutique est active
   */
  static isActive(shop: Shop): boolean {
    return shop.status === 'active';
  }
  
  /**
   * Formate l'adresse d'une boutique
   */
  static formatAddress(shop: Shop): string {
    return shop.address || 'Adresse non spécifiée';
  }
  
  /**
   * Génère une URL de logo par défaut
   */
  static getDefaultLogo(shop: Shop): string {
    const initials = shop.name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
    
    return `https://via.placeholder.com/40x40/4CAF50/FFFFFF?text=${initials}`;
  }
  
  /**
   * Filtre les boutiques selon les critères
   */
  static filterShops(shops: Shop[], filter: ShopFilter): Shop[] {
    return shops.filter(shop => {
      if (filter.name && !shop.name.toLowerCase().includes(filter.name.toLowerCase())) {
        return false;
      }
      
      if (filter.status && !filter.status.includes(shop.status)) {
        return false;
      }
      
      if (filter.location && shop.address && 
          !shop.address.toLowerCase().includes(filter.location.toLowerCase())) {
        return false;
      }
      
      return true;
    });
  }
  
  /**
   * Trie les boutiques
   */
  static sortShops(shops: Shop[], sortBy: keyof Shop, order: 'asc' | 'desc' = 'asc'): Shop[] {
    return [...shops].sort((a, b) => {
      const aValue = a[sortBy];
      const bValue = b[sortBy];
      
      if (aValue === undefined || bValue === undefined) return 0;
      
      const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      return order === 'asc' ? comparison : -comparison;
    });
  }
}

/**
 * Données de test pour les boutiques
 */
export const mockShops: Shop[] = [
  {
    id: 1,
    name: 'Boutique Mode & Style',
    description: 'Vêtements et accessoires de mode',
    status: 'active',
    address: 'Dakar, Sénégal',
    logo: 'https://via.placeholder.com/40x40/4CAF50/FFFFFF?text=MS',
    categories: ['Mode', 'Vêtements', 'Accessoires'],
    contact: {
      phone: '+221 77 123 45 67',
      email: '<EMAIL>'
    },
    owner: {
      name: 'Aminata Diallo',
      email: '<EMAIL>'
    },
    createdAt: '2023-01-15'
  },
  {
    id: 2,
    name: 'Électronique Plus',
    description: 'Appareils électroniques et gadgets',
    status: 'active',
    address: 'Thiès, Sénégal',
    logo: 'https://via.placeholder.com/40x40/2196F3/FFFFFF?text=EP',
    categories: ['Électronique', 'Informatique', 'Gadgets'],
    contact: {
      phone: '+221 77 234 56 78',
      email: '<EMAIL>'
    },
    owner: {
      name: 'Moussa Ndiaye',
      email: '<EMAIL>'
    },
    createdAt: '2023-02-20'
  },
  {
    id: 3,
    name: 'Alimentation Bio',
    description: 'Produits alimentaires biologiques',
    status: 'active',
    address: 'Saint-Louis, Sénégal',
    logo: 'https://via.placeholder.com/40x40/FF9800/FFFFFF?text=AB',
    categories: ['Alimentation', 'Bio', 'Santé'],
    contact: {
      phone: '+221 77 345 67 89',
      email: '<EMAIL>'
    },
    owner: {
      name: 'Fatou Sall',
      email: '<EMAIL>'
    },
    createdAt: '2023-03-10'
  }
];
