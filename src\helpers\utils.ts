export async function downloadFile(url_path: string) {
  try {
    // const response = await api.get(url_path, { responseType: 'blob' });
    const response = await fetch(url_path, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      }
    });
    const result = await response.json();
    const blob = new Blob([result], { type: 'application/xlsx' })
    const link: any = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = url_path
    link.click()
    URL.revokeObjectURL(link.href)

  } catch (error) {
    console.error(error);
  }
}

export async function downloadFileExport(url: string) {
  fetch(url)
    .then(response => response.blob())
    .then(blob => {
      const url = window.URL.createObjectURL(new Blob([blob]));
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = 'file';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
    })
    .catch(() => {
      console.error('Une erreur s\'est produite lors du téléchargement du fichier.');
    });
}


export function makeFileDownload(url_path: string) {
  let newTab = window.open() as Window;
  newTab?.document.write('<html><body><a id="downloadLink" href="' + url_path + '" download> Télécharger le fichier </a></body></html>');
  newTab?.document.close();

  // Attendez que la page soit chargée
  newTab.onload = function () {
    // Obtenez la balise <a>
    let downloadLink = newTab?.document.getElementById('downloadLink');
    // Simulez un clic sur la balise <a>
    downloadLink?.click();
    newTab?.document.close();
  };

}

export function get_role_byName(name: string): number {

  let role_id = 0;
  switch (name) {
    case "CHEF AGENCE":
      role_id = 3;
      break;
    case "AGENT SUPERVISEUR":
      role_id = 4;
      break;
  }
  return role_id;
}

export const get_date_format = (created_at: any) => {
  const date = new Date(created_at);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');
  if (hours !== '00' || minutes !== '00' || seconds !== '00') {
    return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
  } else {
    return `${day}/${month}/${year}`;
  }
}

export const get_amount_format = (amount: any) => {
  return new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'XOF' }).format(amount);
}

export const get_phone_format = (phone: any) => {
  if (phone !== null && phone !== undefined) {
    return phone.replace(/(\d{2})(\d{2})(\d{2})(\d{2})/, "(+228) $1 $2 $3 $4");
  } else {
    return phone;
  }
}

export const get_status_format = (status: string)=>{
  let text = "";
  let color = "";
  switch (status) {
    case "pending":
      text = "En attente";
      color = "orange";
      break;
    case "in_progress":
      text = "En cours";
      color = "blue";
      break;
    case "completed":
      text = "Terminé";
      color = "green";
      break;
    case "partially_paid":
      text = "Partiellement payé";
      color = "yellow";
      break;
    case "paid":
      text = "Payé";
      color = "positive";
      break;
    case "unpaid":
      text = "Non payé";
      color = "negative";
      break;
    case "approved":
      text = "Approuvé";
      color = "green";
      break;
    case "confirmed":
      text = "Confirmé";
      color = "green";
      break;
    case "configured":
      text = "Configuré";
      color = "teal"; // More appropriate for configuration state
      break;
    case "published":
      text = "Publié";
      color = "blue"; // Publication state often associated with blue
      break;
    case "finished":
      text = "Terminé";
      color = "purple"; // Distinct color for completion
      break;
    case "started":
      text = "Démarré";
      color = "cyan"; // Active/running state often shown in cyan
      break;
    case "activated":
      text = "Activé";
      color = "green";
      break;
    case "active":
        text = "Actif";
        color = "green";
        break;
    case "validated":
      text = "Validé";
      color = "green";
      break;
    case "delivered":
      text = "Livré";
      color = "lightgreen";
      break;
    case "canceled":
      text = "Annulé";
      color = "red";
      break;
    case "rejected":
      text = "Rejeté";
      color = "red";
      break;
    case "archived":
      text = "Archivé";
      color = "grey";
      break;
    case "blocked":
      text = "Bloqué";
      color = "black";
      break;
    case "kyc":
      text = "KYC";
      color = "purple";
      break;

    case "new":
      text = "Nouveau";
      color = "blue";
      break;
    case "used":
      text = "En cours d'utilisation";
      color = "green";
      break;
    case "renew":
      text = "Renouvelé";
      color = "blue";
      break;
    default:
      text = "En attente";
      color = "orange";
      break;
  }
  return { text, color };
}

// Fonction pour obtenir les coordonnées géographiques actuelles
export function getCurrentPosition(): Promise<GeolocationPosition> {
  return new Promise((resolve, reject) => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(resolve, reject);
    } else {
      reject(new Error("La géolocalisation n'est pas supportée par ce navigateur."));
    }
  });
}


// Fonction pour générer des positions aléatoires autour d'un point donné
export function generateRandomPositions(lat: number, lon: number, radiusInKm: number = 5, count: number = 50) {
  const positions = [];
  const earthRadiusKm = 6371; // Rayon de la Terre en km

  for (let i = 0; i < count; i++) {
    // Angle aléatoire entre 0 et 2π
    const randomAngle = Math.random() * 2 * Math.PI;
    // Distance aléatoire entre 0 et radiusInKm (10 km max)
    const randomDistance = Math.random() * radiusInKm;

    // Calcul des deltas de latitude et longitude
    const deltaLat = randomDistance * Math.cos(randomAngle) / earthRadiusKm;
    const deltaLon = randomDistance * Math.sin(randomAngle) / (earthRadiusKm * Math.cos(lat * Math.PI / 180));

    const newLat = lat + deltaLat * (180 / Math.PI);
    const newLon = lon + deltaLon * (180 / Math.PI);

    positions.push({
      latitude: newLat,
      longitude: newLon,
      distance: `${randomDistance.toFixed(2)} km`
    });
  }

  return positions;
}


// Exemple d'utilisation avec vos coordonnées
getCurrentPosition()
  .then(position => {
    const latitude = position.coords.latitude;
    const longitude = position.coords.longitude;
    const randomPositions = generateRandomPositions(latitude, longitude);
    // console.log("Positions aléatoires générées :", randomPositions);
  })
  .catch(error => {
    console.error("Erreur lors de l'obtention de la position :", error);
  });












