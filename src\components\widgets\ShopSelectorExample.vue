<script setup lang="ts">
import { ref } from 'vue';
import ShopSelector from './ShopSelector.vue';

// Interface pour la boutique
interface Shop {
  id: number;
  name: string;
  description: string;
  logo?: string;
  status: 'active' | 'inactive';
  address?: string;
}

// État local
const selectedShop = ref<Shop | null>(null);
const notifications = ref<string[]>([]);

// Gestionnaire d'événement pour le changement de boutique
const onShopChanged = (shop: Shop) => {
  console.log('Boutique sélectionnée:', shop);
  
  // Ajouter une notification
  notifications.value.unshift(
    `Boutique changée vers: ${shop.name} (${shop.description})`
  );
  
  // Limiter le nombre de notifications affichées
  if (notifications.value.length > 5) {
    notifications.value = notifications.value.slice(0, 5);
  }
  
  // Ici vous pouvez ajouter d'autres actions comme:
  // - Sauvegarder la sélection dans le localStorage
  // - Mettre à jour le store Pinia/Vuex
  // - Faire un appel API pour charger les données de la boutique
  // - Rediriger vers une page spécifique
  
  // Exemple de sauvegarde dans localStorage
  localStorage.setItem('selectedShop', JSON.stringify(shop));
};

// Fonction pour effacer les notifications
const clearNotifications = () => {
  notifications.value = [];
};

// Fonction pour simuler une action avec la boutique sélectionnée
const performActionWithShop = () => {
  if (selectedShop.value) {
    alert(`Action effectuée pour la boutique: ${selectedShop.value.name}`);
  } else {
    alert('Veuillez d\'abord sélectionner une boutique');
  }
};
</script>

<template>
  <div class="shop-selector-example q-pa-md">
    <div class="text-h5 q-mb-md">Exemple d'utilisation du ShopSelector</div>
    
    <!-- Composant ShopSelector -->
    <div class="q-mb-lg">
      <ShopSelector
        v-model="selectedShop"
        label="Choisissez votre boutique"
        @shop-changed="onShopChanged"
        outlined
      />
    </div>

    <!-- Informations sur la boutique sélectionnée -->
    <div v-if="selectedShop" class="q-mb-lg">
      <q-card flat bordered>
        <q-card-section>
          <div class="text-h6 text-primary q-mb-sm">Boutique Actuelle</div>
          <div class="row items-center">
            <q-avatar size="60px" class="q-mr-md">
              <q-img 
                :src="selectedShop.logo" 
                :alt="selectedShop.name"
                spinner-color="primary"
              />
            </q-avatar>
            <div class="col">
              <div class="text-h6">{{ selectedShop.name }}</div>
              <div class="text-subtitle2 text-grey-6">{{ selectedShop.description }}</div>
              <div class="text-body2 text-grey-5">
                <q-icon name="place" class="q-mr-xs" />
                {{ selectedShop.address }}
              </div>
            </div>
            <div>
              <q-badge 
                :color="selectedShop.status === 'active' ? 'positive' : 'grey'" 
                :label="selectedShop.status === 'active' ? 'Actif' : 'Inactif'"
                rounded
              />
            </div>
          </div>
        </q-card-section>
        <q-card-actions>
          <q-btn 
            color="primary" 
            label="Effectuer une action"
            @click="performActionWithShop"
            icon="store"
          />
        </q-card-actions>
      </q-card>
    </div>

    <!-- Notifications des changements -->
    <div v-if="notifications.length > 0" class="q-mb-lg">
      <q-card flat bordered>
        <q-card-section>
          <div class="row items-center justify-between q-mb-sm">
            <div class="text-h6">Historique des changements</div>
            <q-btn 
              flat 
              dense 
              icon="clear_all" 
              label="Effacer"
              @click="clearNotifications"
              size="sm"
            />
          </div>
          <q-list separator>
            <q-item 
              v-for="(notification, index) in notifications" 
              :key="index"
              dense
            >
              <q-item-section avatar>
                <q-icon name="info" color="primary" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ notification }}</q-item-label>
                <q-item-label caption>
                  {{ new Date().toLocaleTimeString() }}
                </q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>
      </q-card>
    </div>

    <!-- Instructions d'utilisation -->
    <q-card flat bordered>
      <q-card-section>
        <div class="text-h6 q-mb-sm">Comment utiliser ce composant</div>
        <q-list>
          <q-item>
            <q-item-section avatar>
              <q-icon name="code" color="primary" />
            </q-item-section>
            <q-item-section>
              <q-item-label>Import du composant</q-item-label>
              <q-item-label caption>
                import ShopSelector from 'src/components/widgets/ShopSelector.vue'
              </q-item-label>
            </q-item-section>
          </q-item>
          
          <q-item>
            <q-item-section avatar>
              <q-icon name="settings" color="primary" />
            </q-item-section>
            <q-item-section>
              <q-item-label>Props disponibles</q-item-label>
              <q-item-label caption>
                modelValue, disabled, outlined, dense, label
              </q-item-label>
            </q-item-section>
          </q-item>
          
          <q-item>
            <q-item-section avatar>
              <q-icon name="event" color="primary" />
            </q-item-section>
            <q-item-section>
              <q-item-label>Événements émis</q-item-label>
              <q-item-label caption>
                @update:modelValue, @shop-changed
              </q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </q-card-section>
    </q-card>
  </div>
</template>

<style scoped>
.shop-selector-example {
  max-width: 800px;
  margin: 0 auto;
}
</style>
