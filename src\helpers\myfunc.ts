import { UserToken } from "src/models";

function setCookie(c_name: string, c_value: string) {

  var d = new Date();
  d.setTime(d.getTime() + (1 * 24 * 60 * 60 * 1000));
  var expires = "expires=" + d.toUTCString();
  document.cookie = c_name + "=" + c_value + ";" + expires + ";path=/";
}

/**
 * getCookie function
 * Permet de retourner la valeur d'un cookie
 * @param {string} cname
 * @returns value
 */
function getCookie(cname: string) {
  let name = cname + "=";
  let decodedCookie = decodeURIComponent(document.cookie);
  let ca = decodedCookie.split(';');
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i] as string;
    while (c.charAt(0) == ' ') {
      c = c.substring(1);
    }
    if (c.indexOf(name) == 0) {
      return c.substring(name.length, c.length);
    }
  }
  return "";
}

function getToken(): string | null {
  // @ts-ignore
  const userToken = JSON.parse(localStorage.getItem('token')) as UserToken;
  if (!userToken) {
    return null;
  }

  if (userToken && typeof userToken === 'object') {
    return userToken.token;
  } else {
    return null;
  }
}

function checkToken() {
  // @ts-ignore
  const expire_date = JSON.parse(localStorage.getItem('tokenExpiration')) as string;
  const tokenExpiration = convertToTime(expire_date);
  let is_auth = true;
  const now = new Date().getTime();
  if (now - tokenExpiration >= 8 * 60 * 60 * 1000) {
    localStorage.removeItem('token');
    localStorage.removeItem('tokenExpiration');
    console.log('Le jeton d\'authentification a été supprimé.');
    is_auth = false;
  }
  return is_auth;
}

function convertToTime(dateString: string) {
  const dateObj = new Date(dateString);
  return dateObj.getTime();
}


function addHours(date: { getTime: () => number; }, hours: number) {
  return new Date(date.getTime() + hours * 60 * 60 * 1000);
}

function getExpires() {
  var d = new Date();

  d.setTime(d.getTime() + (1 * 24 * 60 * 60 * 1000));
  var expires = "expires=" + d.toUTCString();
  return expires;
}

/**
 *deleteCookie function
 * Permet de supprimer un cookie
 * @param {string} cname
 */
function deleteCookie(cname: string) {
  document.cookie = cname + '=; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
}


const random = (length = 8) => {
  // Declare all characters
  let chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  // Pick characers randomly
  let str = '';
  for (let i = 0; i < length; i++) {
    str += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return str;
};

export {
  setCookie, getCookie, getExpires, deleteCookie, random, getToken, checkToken,
  convertToTime
};
