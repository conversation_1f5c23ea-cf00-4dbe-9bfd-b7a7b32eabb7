<script lang="ts">
import { defineComponent, ref } from 'vue';
import ShopSelectorExample from 'src/components/widgets/ShopSelectorExample.vue';

export default defineComponent({
  name: 'TestShopSelector',
  components: {
    ShopSelectorExample,
  },
  setup() {
    return {};
  },
});
</script>

<template>
  <q-page class="q-pa-md">
    <div class="text-h4 q-mb-lg text-center">
      Test du ShopSelector intégré dans MainLayout
    </div>
    
    <q-card flat bordered class="q-mb-lg">
      <q-card-section>
        <div class="text-h6 q-mb-sm">Instructions de test</div>
        <q-list>
          <q-item>
            <q-item-section avatar>
              <q-icon name="info" color="primary" />
            </q-item-section>
            <q-item-section>
              <q-item-label>Le ShopSelector est maintenant intégré dans la sidebar du MainLayout</q-item-label>
              <q-item-label caption>
                Regardez dans la sidebar à gauche, juste sous le logo MASSANOU MARKET
              </q-item-label>
            </q-item-section>
          </q-item>
          
          <q-item>
            <q-item-section avatar>
              <q-icon name="store" color="primary" />
            </q-item-section>
            <q-item-section>
              <q-item-label>Sélectionnez une boutique</q-item-label>
              <q-item-label caption>
                Le nom de la boutique sélectionnée apparaîtra dans le header principal
              </q-item-label>
            </q-item-section>
          </q-item>
          
          <q-item>
            <q-item-section avatar>
              <q-icon name="save" color="primary" />
            </q-item-section>
            <q-item-section>
              <q-item-label>Sauvegarde automatique</q-item-label>
              <q-item-label caption>
                La boutique sélectionnée est sauvegardée dans localStorage
              </q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </q-card-section>
    </q-card>

    <!-- Composant d'exemple pour tester les fonctionnalités -->
    <ShopSelectorExample />
    
    <!-- Informations techniques -->
    <q-card flat bordered class="q-mt-lg">
      <q-card-section>
        <div class="text-h6 q-mb-sm">Informations techniques</div>
        <q-list>
          <q-item>
            <q-item-section avatar>
              <q-icon name="code" color="primary" />
            </q-item-section>
            <q-item-section>
              <q-item-label>Composant principal</q-item-label>
              <q-item-label caption>
                src/components/widgets/ShopSelector.vue
              </q-item-label>
            </q-item-section>
          </q-item>
          
          <q-item>
            <q-item-section avatar>
              <q-icon name="integration_instructions" color="primary" />
            </q-item-section>
            <q-item-section>
              <q-item-label>Intégration</q-item-label>
              <q-item-label caption>
                Intégré dans src/layouts/MainLayout.vue
              </q-item-label>
            </q-item-section>
          </q-item>
          
          <q-item>
            <q-item-section avatar>
              <q-icon name="event" color="primary" />
            </q-item-section>
            <q-item-section>
              <q-item-label>Événements</q-item-label>
              <q-item-label caption>
                @shop-changed émis lors de la sélection
              </q-item-label>
            </q-item-section>
          </q-item>
          
          <q-item>
            <q-item-section avatar>
              <q-icon name="storage" color="primary" />
            </q-item-section>
            <q-item-section>
              <q-item-label>Persistance</q-item-label>
              <q-item-label caption>
                Sauvegarde dans localStorage avec la clé 'currentShop'
              </q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<style scoped>
/* Styles spécifiques à la page de test */
</style>
