import { DateTime } from 'luxon';

export type MenuItem = {
  title: string;
  value: string;
  path: string;
  icon?: string;
}

export type Menu = {
  icon?: string;
  text: string;
  value: string;
  path: string;
  childreen: MenuItem[];
}

// Interface générique pour la réponse API
export interface Response {
  success: boolean;
  message: string;
  result: any;
  errors?: any;
  except?: any;
}

// Interface pour les jetons d'authentification
export interface UserToken {
  token: string;
  expires_at: string;
  type?: string;
}

// Interface pour la table `admins`
export interface Admin {
  id: number;
  public_id: string;
  user_id: number;
  access_type?: string;
  last_name: string;
  first_name: string;
  phone?: string;
  email: string;
  permissions?: Record<string, any>;
  created_at?: DateTime;
  updated_at?: DateTime;
  // Relations
  user: User; // Relation vers `users` (FOREIGN KEY `user_id` ON DELETE CASCADE)
}

// Interface pour la table `auth_access_tokens`
export interface AuthAccessToken {
  id: number;
  tokenable_id: number;
  type: string;
  name?: string;
  hash: string;
  abilities: string;
  created_at?: DateTime;
  updated_at?: DateTime;
  last_used_at?: DateTime;
  expires_at?: DateTime;
  // Relations
  tokenable: User; // Relation vers `users` (FOREIGN KEY `tokenable_id` ON DELETE CASCADE)
}

// Interface pour la table `categories`
export interface Category {
  id: number;
  public_id: string;
  domain_id: number;
  parent_id?: number;
  name: string;
  slug?: string;
  description?: string;
  image?: string;
  created_at?: DateTime;
  updated_at?: DateTime;
  // Relations
  domain?: Domain; // Relation vers `domains` (FOREIGN KEY `domain_id` ON DELETE CASCADE)
  parent?: Category; // Relation vers `categories` (FOREIGN KEY `parent_id` ON DELETE CASCADE)
}

// Interface pour la table `commissions`
export interface Commission {
  id: number;
  order_id: number;
  order_item_id: number;
  shop_id: number;
  item_amount: number;
  platform_fee_percentage: number;
  platform_fee_amount: number;
  seller_amount: number;
  created_at?: DateTime;
  updated_at?: DateTime;
  // Relations
  order: Order; // Relation vers `orders` (FOREIGN KEY `order_id` ON DELETE CASCADE)
  order_item: OrderItem; // Relation vers `order_items` (FOREIGN KEY `order_item_id` ON DELETE CASCADE)
  shop: Shop; // Relation vers `shops` (FOREIGN KEY `shop_id` ON DELETE CASCADE)
}

// Interface pour la table `countries`
export interface Country {
  id: number;
  name: string;
  code: string;
  prefix: string;
  flag?: string;
  currency?: string;
  created_at?: DateTime;
  updated_at?: DateTime;
  // Pas de relations sortantes (utilisée comme référence par d'autres tables)
}

// Interface pour la table `coupons`
export interface Coupon {
  id: number;
  code: string;
  shop_id?: number;
  discount_amount: number;
  discount_type: 'percentage' | 'fixed';
  usage_limit?: number;
  times_used: number;
  is_active: boolean;
  valid_from: DateTime;
  valid_until?: DateTime;
  description?: string;
  created_at?: DateTime;
  updated_at?: DateTime;
  // Relations
  shop?: Shop; // Relation vers `shops` (FOREIGN KEY `shop_id` ON DELETE CASCADE)
}

// Interface pour la table `customers`
export interface Customer {
  id: number;
  public_id: string;
  user_id: number;
  country_id: number;
  first_name: string;
  last_name: string;
  phone?: string;
  email?: string;
  birthdate?: string;
  gender?: 'M' | 'F';
  address?: string;
  city?: string;
  state?: string;
  status: 'pending' | 'active' | 'inactive';
  created_at?: DateTime;
  updated_at?: DateTime;
  // Relations
  user?: User; // Relation vers `users` (FOREIGN KEY `user_id` ON DELETE CASCADE)
  country?: Country; // Relation vers `countries` (FOREIGN KEY `country_id` ON DELETE CASCADE)
}

// Interface pour la table `domains`
export interface Domain {
  id: number;
  public_id: string;
  name: string;
  description?: string;
  is_active: boolean;
  created_at?: DateTime;
  updated_at?: DateTime;
  // Pas de relations sortantes (utilisée comme référence par d'autres tables)
}

// Interface pour la table `order_items`
export interface OrderItem {
  id: number;
  public_id: string;
  order_id: number;
  product_id: number;
  shop_id: number;
  quantity: number;
  price: number;
  status: 'pending' | 'processing' | 'completed' | 'cancelled';
  payment_status: 'pending' | 'processing' | 'paid' | 'failed' | 'refunded';
  is_paid: boolean;
  created_at?: DateTime;
  updated_at?: DateTime;
  // Relations
  order: Order; // Relation vers `orders` (FOREIGN KEY `order_id` ON DELETE CASCADE)
  product: Product; // Relation vers `products` (FOREIGN KEY `product_id` ON DELETE CASCADE)
  shop: Shop; // Relation vers `shops` (FOREIGN KEY `shop_id` ON DELETE CASCADE)
}

// Interface pour la table `orders`
export interface Order {
  id: number;
  public_id: string;
  customer_id: number;
  subtotal_amount: number;
  tax: number;
  shipping_cost: number;
  total_amount: number;
  currency: string;
  status: 'pending' | 'processing' | 'completed' | 'cancelled';
  payment_status: 'pending' | 'processing' | 'paid' | 'failed' | 'refunded';
  delivery_status: 'pending' | 'processing' | 'delivered' | 'cancelled';
  is_paid: boolean;
  requires_shipping: boolean;
  shipping_details?: {
    address: string;
    city: string;
    state: string;
    country: string;
    postal_code: string;
    phone: string;
  };
  tracking_number?: string;
  shipping_method?: string;
  estimated_delivery_date?: DateTime;
  created_at?: DateTime;
  updated_at?: DateTime;
  // Relations
  customer: Customer; // Relation vers `customers` (FOREIGN KEY `customer_id` ON DELETE CASCADE)
}

// Interface pour la table `payment_gateways`
export interface PaymentGateway {
  id: number;
  name?: string;
  type_api: 'momo' | 'bank' | 'virtual' | 'crypto';
  api_key?: string;
  merchant_id?: string;
  mode: 'sandbox' | 'live';
  create_transact_url?: string;
  check_transact_url?: string;
  cashout_create_transact_url?: string;
  cashout_check_transact_url?: string;
  check_balance_url?: string;
  pricing?: Record<string, any>;
  logo?: string;
  actions?: Record<string, any>;
  status: boolean;
  created_at?: DateTime;
  updated_at?: DateTime;
  // Pas de relations sortantes
}

// Interface pour la table `payment_intents`
export interface PaymentIntent {
  id: number;
  public_id: string;
  reference?: string;
  order_id: number;
  user_id?: number;
  currency: string;
  amount_paid: number;
  fees?: number;
  payment_method: 'momo' | 'bank' | 'virtual' | 'crypto';
  phone?: string;
  network?: string;
  metadata?: Record<string, any>;
  client_info?: Record<string, any>;
  payment_link?: string;
  callback_url?: string;
  redirect_url?: string;
  qrcode_url?: string;
  status: 'pending' | 'paid' | 'cancelled' | 'failed' | 'success';
  description?: string;
  request_at?: DateTime;
  paid_at?: DateTime;
  canceled_at?: DateTime;
  created_at?: DateTime;
  updated_at?: DateTime;
  // Relations
  order: Order; // Relation vers `orders` (FOREIGN KEY `order_id` ON DELETE CASCADE)
  user?: User; // Relation vers `users` (FOREIGN KEY `user_id` ON DELETE CASCADE)
}

// Interface pour la table `product_types`
export interface ProductType {
  id: number;
  public_id: string;
  category_id: number;
  name: string;
  slug?: string;
  description?: string;
  created_at?: DateTime;
  updated_at?: DateTime;
  // Relations
  category: Category; // Relation vers `categories` (FOREIGN KEY `category_id` ON DELETE CASCADE)
}

// Interface pour la table `products`
export interface Product {
  id: number;
  public_id: string;
  shop_id: number;
  product_type_id: number;
  name: string;
  slug?: string;
  sku?: string;
  description?: string;
  featured_image?: string;
  gallery?: { url: string; alt: string }[];
  video_url?: string;
  condition: 'new' | 'used';
  status: 'draft' | 'published' | 'archived';
  is_featured: boolean;
  is_digital: boolean;
  digital_files?: { url: string; name: string }[];
  price: number;
  sale_price?: number;
  quantity?: number;
  attributes?: { name: string; value: string }[];
  tags?: { id: number; name: string }[];
  has_custom_checkout: boolean;
  has_custom_thank_you: boolean;
  checkout_page_title?: string;
  checkout_page_template?: string;
  checkout_page_content?: string;
  thank_you_page_content?: string;
  checkout_page_settings?: Record<string, any>;
  thank_you_page_settings?: Record<string, any>;
  average_rating: number;
  total_ratings: number;
  rating_counts?: { [key: string]: number };
  published_at?: DateTime;
  archived_at?: DateTime;
  created_at?: DateTime;
  updated_at?: DateTime;
  // Relations
  shop: Shop; // Relation vers `shops` (FOREIGN KEY `shop_id` ON DELETE CASCADE)
  product_type: ProductType; // Relation vers `product_types` (FOREIGN KEY `product_type_id` ON DELETE CASCADE)
}

// Interface pour la table `questions`
export interface Question {
  id: number;
  customer_id?: number;
  product_id?: number;
  shop_id?: number;
  question: string;
  answer: string;
  is_published: boolean;
  views_count: number;
  helpful_count: number;
  not_helpful_count: number;
  created_at?: DateTime;
  updated_at?: DateTime;
  // Relations
  customer?: Customer; // Relation vers `customers` (FOREIGN KEY `customer_id` ON DELETE CASCADE)
  product?: Product; // Relation vers `products` (FOREIGN KEY `product_id` ON DELETE CASCADE)
  shop?: Shop; // Relation vers `shops` (FOREIGN KEY `shop_id` ON DELETE CASCADE)
}

// Interface pour la table `reviews`
export interface Review {
  id: number;
  customer_id?: number;
  product_id?: number;
  shop_id?: number;
  rating: number;
  comment?: string;
  likes: number;
  dislikes: number;
  created_at?: DateTime;
  updated_at?: DateTime;
  // Relations
  customer?: Customer; // Relation vers `customers` (FOREIGN KEY `customer_id` ON DELETE CASCADE)
  product?: Product; // Relation vers `products` (FOREIGN KEY `product_id` ON DELETE CASCADE)
  shop?: Shop; // Relation vers `shops` (FOREIGN KEY `shop_id` ON DELETE CASCADE)
}

// Interface pour la table `roles`
export interface Role {
  id: number;
  public_id: string;
  name: string;
  description?: string;
  created_at?: DateTime;
  updated_at?: DateTime;
  // Pas de relations sortantes
}

// Interface pour la table `sellers`
export interface Seller {
  id: number;
  public_id: string;
  user_id: number;
  country_id: number;
  first_name: string;
  last_name: string;
  phone?: string;
  email: string;
  birthdate?: string;
  gender?: 'M' | 'F';
  address?: string;
  city?: string;
  state?: string;
  is_verified: boolean;
  status: 'pending' | 'active' | 'inactive';
  created_at?: DateTime;
  updated_at?: DateTime;
  // Relations
  user: User; // Relation vers `users` (FOREIGN KEY `user_id` ON DELETE CASCADE)
  country: Country; // Relation vers `countries` (FOREIGN KEY `country_id` ON DELETE CASCADE)
}

// Interface pour la table `shop_sellers`
export interface ShopSeller {
  id: number;
  shop_id?: number;
  seller_id?: number;
  seller_type: 'owner' | 'staff';
  permissions?: Record<string, any>;
  is_active: boolean;
  last_login_at?: DateTime;
  created_at?: DateTime;
  updated_at?: DateTime;
  // Relations
  shop?: Shop; // Relation vers `shops` (FOREIGN KEY `shop_id` ON DELETE CASCADE)
  seller?: Seller; // Relation vers `sellers` (FOREIGN KEY `seller_id` ON DELETE CASCADE)
}

// Interface pour la table `shops`
export interface Shop {
  id: number;
  public_id: string;
  domain_id?: number;
  name: string;
  slug?: string;
  logo?: string;
  cover?: string;
  website_url?: string;
  email?: string;
  phone?: string;
  description?: string;
  country_id: number;
  city?: string;
  state?: string;
  address: string;
  social_profiles?: { name: string; url: string }[];
  working_hours?: { day: string; start: string; end: string }[];
  payment_methods?: { type: 'bank' | 'mobile_money' }[];
  has_custom_checkout: boolean;
  has_custom_thank_you: boolean;
  checkout_page_title?: string;
  checkout_page_template?: string;
  checkout_page_content?: string;
  thank_you_page_content?: string;
  checkout_page_settings?: Record<string, any>;
  thank_you_page_settings?: Record<string, any>;
  created_at?: DateTime;
  updated_at?: DateTime;
  // Relations
  domain?: Domain; // Relation vers `domains` (FOREIGN KEY `domain_id` ON DELETE CASCADE)
  country: Country; // Relation vers `countries` (FOREIGN KEY `country_id` ON DELETE CASCADE)
}

// Interface pour la table `tags`
export interface Tag {
  id: number;
  public_id: string;
  name: string;
  created_at?: DateTime;
  updated_at?: DateTime;
  // Pas de relations sortantes
}

// Interface pour la table `transactions`
export interface Transaction {
  id: number;
  public_id: string;
  reference?: string;
  order_id: number;
  customer_id?: number;
  payment_intent_id?: number;
  currency: string;
  amount: number;
  fees?: number;
  payment_method: 'momo' | 'bank' | 'virtual' | 'crypto';
  payment_data?: Record<string, any>;
  status: 'pending' | 'paid' | 'cancelled' | 'failed' | 'success';
  description?: string;
  paid_at?: DateTime;
  created_at?: DateTime;
  updated_at?: DateTime;
  // Relations
  order: Order; // Relation vers `orders` (FOREIGN KEY `order_id` ON DELETE CASCADE)
  customer?: Customer; // Relation vers `customers` (FOREIGN KEY `customer_id` ON DELETE CASCADE)
  payment_intent?: PaymentIntent; // Relation vers `payment_intents` (FOREIGN KEY `payment_intent_id` ON DELETE CASCADE)
}

// Interface pour la table `users`
export interface User {
  id: number;
  public_id: string;
  username?: string;
  email?: string;
  phone?: string;
  role_id: number;
  country_id: number;
  email_verified_at?: DateTime;
  phone_verified_at?: DateTime;
  email_verification_code?: string;
  sms_verification_code?: string;
  email_code_expires_at?: DateTime;
  sms_code_expires_at?: DateTime;
  token?: string;
  auth_method: 'password' | 'email_code' | 'sms_code';
  password?: string;
  avatar?: string;
  is_online: boolean;
  status: 'pending' | 'active' | 'inactive';
  created_at: DateTime;
  updated_at?: DateTime;
  // Relations
  role?: Role; // Relation vers `roles` (FOREIGN KEY `role_id` ON DELETE CASCADE)
  country?: Country; // Relation vers `countries` (FOREIGN KEY `country_id` ON DELETE CASCADE)
}

// Interface pour la table `wallet_histories`
export interface WalletHistory {
  id: number;
  wallet_id?: number;
  type: 'credit' | 'debit';
  amount: number;
  previous_balance: number;
  new_balance: number;
  description?: string;
  reference?: string;
  status: string;
  metadata?: Record<string, any>;
  created_at?: DateTime;
  updated_at?: DateTime;
  // Relations
  wallet?: Wallet; // Relation vers `wallets` (FOREIGN KEY `wallet_id` ON DELETE CASCADE)
}

// Interface pour la table `wallets`
export interface Wallet {
  id: number;
  public_id: string;
  name?: string;
  owner_type: 'customer' | 'business' | 'organization';
  owner_id?: number;
  currency: string;
  balance: number;
  status: 'active' | 'inactive' | 'pending' | 'blocked';
  wallet_code?: string;
  secret_code?: string;
  has_secret_code: boolean;
  description?: string;
  created_at?: DateTime;
  updated_at?: DateTime;
  // Pas de relations sortantes explicites (owner_id non lié par contrainte FK)
}

// Interface pour la table `withdrawals`
export interface Withdrawal {
  id: number;
  public_id: string;
  wallet_id: number;
  amount: number;
  fees?: number;
  status: 'pending' | 'approved' | 'rejected' | 'failed' | 'completed';
  description?: string;
  metadata?: Record<string, any>;
  created_at?: DateTime;
  updated_at?: DateTime;
  // Relations
  wallet: Wallet; // Relation vers `wallets` (FOREIGN KEY `wallet_id` ON DELETE CASCADE)
}

