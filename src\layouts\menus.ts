import { Menu } from "src/models";

// Menus pour l'espace vendeur

export const mainLinks: Menu[] = [
  {
    icon: 'dashboard',
    text: 'Tableau de bord',
    value: 'dashboard',
    path: '',
    childreen: []
  }
];

export const shopLinks: Menu[] = [
  {
    icon: 'store',
    text: 'Mes boutiques',
    value: 'shops',
    path: '/shops',
    childreen: []
  },
  {
    icon: 'inventory_2',
    text: 'Mes produits',
    value: 'products',
    path: '/products',
    childreen: []
  },
  {
    icon: 'local_offer',
    text: 'Coupons de réduction',
    value: 'coupons',
    path: '/coupons',
    childreen: []
  },
  {
    icon: 'local_activity',
    text: 'Promotions',
    value: 'promotions',
    path: '/promotions',
    childreen: []
  }
];

export const ordersLinks: Menu[] = [
  {
    icon: 'shopping_cart',
    text: 'Mes commandes',
    value: 'orders',
    path: '/orders',
    childreen: []
  },
  {
    icon: 'swap_horiz',
    text: 'Transactions',
    value: 'transactions',
    path: '/transactions',
    childreen: []
  },
  {
    icon: 'account_balance_wallet',
    text: 'Portefeuille',
    value: 'wallets',
    path: '/wallets',
    childreen: []
  }
];

export const feedbackLinks: Menu[] = [
  {
    icon: 'star',
    text: 'Avis',
    value: 'reviews',
    path: '/reviews',
    childreen: []
  },
  {
    icon: 'question_answer',
    text: 'Questions',
    value: 'questions',
    path: '/questions',
    childreen: []
  }
];

export const settingsLinks: Menu[] = [
  {
    icon: 'settings',
    text: 'Paramètres boutique',
    value: 'shop-settings',
    path: '/shop-settings',
    childreen: []
  }
];

const menus = {
  main: mainLinks,
  shop: shopLinks,
  orders: ordersLinks,
  feedback: feedbackLinks,
  settings: settingsLinks
};

// Réorganisation avec titres adaptés pour l'espace vendeur
export const menuSections = [
  {
    title: "Principal",
    links: menus.main
  },
  {
    title: "Gestion boutique",
    links: menus.shop
  },
  {
    title: "Gestion des commandes",
    links: menus.orders
  },
  {
    title: "Retours clients",
    links: menus.feedback
  },
  {
    title: "Paramètres",
    links: menus.settings
  }
];
